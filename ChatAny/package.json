{"name": "chatany", "private": false, "license": "mit", "scripts": {"mask": "npx tsx app/masks/build.ts", "mask:watch": "npx watch 'yarn mask' app/masks", "dev": "yarn run mask:watch & next dev", "build": "yarn mask && cross-env BUILD_MODE=standalone next build", "start": "next start", "lint": "next lint", "export": "yarn mask && cross-env BUILD_MODE=export BUILD_APP=1 next build", "export:dev": "yarn mask:watch & cross-env BUILD_MODE=export BUILD_APP=1 next dev", "app:dev": "yarn mask:watch & yarn tauri dev", "app:build": "yarn mask && yarn tauri build", "prompts": "node ./scripts/fetch-prompts.mjs", "prepare": "husky install", "proxy-dev": "sh ./scripts/init-proxy.sh && proxychains -f ./scripts/proxychains.conf yarn dev"}, "dependencies": {"@fortaine/fetch-event-source": "^3.0.6", "@hello-pangea/dnd": "^16.5.0", "@next/third-parties": "^14.1.0", "@svgr/webpack": "^6.5.1", "@vercel/analytics": "^0.1.11", "@vercel/speed-insights": "^1.0.2", "deepcopy": "^2.1.0", "emoji-picker-react": "^4.9.2", "fuse.js": "^7.0.0", "heic2any": "^0.0.4", "html-to-image": "^1.11.11", "mermaid": "^10.6.1", "nanoid": "^5.0.3", "next": "^14.1.1", "node-fetch": "^3.3.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^8.0.7", "react-mask-editor": "^0.0.2", "react-router-dom": "^6.15.0", "rehype-highlight": "^6.0.0", "rehype-katex": "^6.0.3", "remark-breaks": "^3.0.2", "remark-gfm": "^3.0.1", "remark-math": "^5.1.1", "sass": "^1.59.2", "spark-md5": "^3.0.2", "use-debounce": "^9.0.4", "zustand": "^4.3.8"}, "devDependencies": {"@tauri-apps/cli": "1.5.11", "@types/node": "^20.11.30", "@types/react": "^18.2.70", "@types/react-dom": "^18.2.7", "@types/react-katex": "^3.0.0", "@types/spark-md5": "^3.0.4", "cross-env": "^7.0.3", "eslint": "^8.49.0", "eslint-config-next": "13.4.19", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^5.1.3", "husky": "^8.0.0", "lint-staged": "^13.2.2", "prettier": "^3.0.2", "tsx": "^4.16.0", "typescript": "5.2.2", "watch": "^1.0.2", "webpack": "^5.88.1"}, "resolutions": {"lint-staged/yaml": "^2.2.2"}, "packageManager": "yarn@1.22.19"}