.message-selector {
  .message-filter {
    display: flex;

    .search-bar {
      max-width: unset;
      flex-grow: 1;
      margin-right: 10px;
    }

    .actions {
      display: flex;

      button:not(:last-child) {
        margin-right: 10px;
      }
    }

    @media screen and (max-width: 600px) {
      flex-direction: column;

      .search-bar {
        margin-right: 0;
      }

      .actions {
        margin-top: 20px;

        button {
          flex-grow: 1;
        }
      }
    }
  }

  .messages {
    margin-top: 20px;
    border-radius: 10px;
    border: var(--border-in-light);
    overflow: hidden;

    .message {
      display: flex;
      align-items: center;
      padding: 8px 10px;
      cursor: pointer;

      &-selected {
        background-color: var(--second);
      }

      &:not(:last-child) {
        border-bottom: var(--border-in-light);
      }

      .avatar {
        margin-right: 10px;
      }

      .body {
        flex: 1;
        max-width: calc(100% - 80px);

        .date {
          font-size: 12px;
          line-height: 1.2;
          opacity: 0.5;
        }

        .content {
          font-size: 12px;
        }
      }

      .checkbox {
        display: flex;
        justify-content: flex-end;
        flex: 1;
      }
    }
  }
}
