.ctrl-param-item {
  display: flex;
  justify-content: space-between;
  min-height: 40px;
  padding: 10px 0;
  animation: slide-in ease 0.6s;
  flex-direction: column;

  .ctrl-param-item-header {
    display: flex;
    align-items: center;

    .ctrl-param-item-title {
      font-size: 14px;
      font-weight: bolder;
      margin-bottom: 5px;
    }
  }

  .ctrl-param-item-sub-title {
    font-size: 12px;
    font-weight: normal;
    margin-top: 3px;
  }
  textarea {
    appearance: none;
    border-radius: 10px;
    border: var(--border-in-light);
    min-height: 36px;
    box-sizing: border-box;
    background: var(--white);
    color: var(--black);
    padding: 0 10px;
    max-width: 50%;
    font-family: inherit;
  }
}

.ai-models {
  button {
    margin-bottom: 10px;
    padding: 10px;
    width: 100%;
  }
}
