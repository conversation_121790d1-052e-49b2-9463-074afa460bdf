.mj-img-list{
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  .mj-img-item-box{
    width: 48%;
    &:not(:last-child){
      margin-bottom: 20px;
    }
  }
  .mj-img-item{
    .mj-img-item-info{
      flex:1;
      width: 100%;
      overflow: hidden;
      user-select: text;
      p{
        margin: 6px;
        font-size: 12px;
      }
      .line-1{
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    .pre-img{
      display: flex;
      width: 130px;
      justify-content: center;
      align-items: center;
      background-color: var(--second);
      border-radius: 10px;
    }
    .pre-img-loading{
      flex-direction: column;
      font-size: 12px;
    }
    .img{
      width: 130px;
      height: 130px;
      border-radius: 10px;
      overflow: hidden;
      cursor: pointer;
      transition: all .3s;
      &:hover{
        opacity: .7;
      }
    }
  }
}

@media only screen and (max-width: 600px) {
  .mj-img-list{
    .mj-img-item-box{
      width: 100%;
    }
  }
}