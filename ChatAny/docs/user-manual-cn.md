# 用户手册 User Manual

> No english version yet, please read this doc with ChatGPT or other translation tools.

本文档用于解释 NextChat 的部分功能介绍和设计原则。

## 面具 (Mask)

### 什么是面具？它和提示词的区别是什么？

面具 = 多个预设提示词 + 模型设置 + 对话设置。

其中预设提示词（Contextual Prompts）一般用于 In-Context Learning，用于让 ChatGPT 生成更加符合要求的输出，也可以增加系统约束或者输入有限的额外知识。

模型设置则顾名思义，使用此面具创建的对话都会默认使用对应的模型参数。

对话设置是与对话体验相关的一系列设置，我们会在下方的章节中依次介绍。

### 如何添加一个预设面具？

目前仅能够通过编辑源代码的方式添加预设面具，请根据需要编辑 [mask](../app/masks/) 目录下对应语言的文件即可。

编辑步骤如下：

1. 在 NextChat 中配置好一个面具；
2. 使用面具编辑页面的下载按钮，将面具保存为 JSON 格式；
3. 让 ChatGPT 帮你将 json 文件格式化为对应的 ts 代码；
4. 放入对应的 .ts 文件。

后续会增加使用旁加载的方式加载面具。

## 对话 (Chat)

### 对话框上方的按钮的作用

在默认状态下，将鼠标移动到按钮上，即可查看按钮的文字说明，我们依次介绍：

- 对话设置：当前对话的设置，它与全局设置的关系，请查看下一小节的说明；
- 颜色主题：点击即可在自动、暗黑、浅色之间轮换；
- 快捷指令：项目内置的快捷填充预设提示词，也可以在对话框中输入 / 进行搜索；
- 所有面具：进入面具页面；
- 清除聊天：插入一个清除标记，标记上方的聊天将不会发给 GPT，效果相当于清除了当前对话，当然，你也可以再次点击该按钮，可取消清除；
- 模型设置：更改当前对话的模型，注意，此按钮只会修改当前对话的模型，并不会修改全局默认模型。

### 对话内设置与全局设置的关系

目前有两处设置入口：

1. 页面左下角的设置按钮，进入后是全局设置页；
2. 对话框上方的设置按钮，进入后是对话设置页。

在新建对话后，该对话的设置默认与全局设置保持同步，修改全局设置，则新建对话的对话内设置也会被同步修改。

一旦用户手动更改过对话内设置，则对话内设置将与全局设置断开同步，此时更改全局设置，将不会对该对话生效。

如果想恢复两者的同步关系，可以将“对话内设置 -> 使用全局设置”选项勾选。

### 对话内设置项的含义

点开对话框上方的按钮，进入对话内设置，内容从上到下依次为：

- 预设提示词列表：可以增加、删除、排序预设提示词
- 角色头像：顾名思义
- 角色名称：顾名思义
- 隐藏预设对话：隐藏后，预设提示词不会出现在聊天界面
- 使用全局设置：用于表示当前对话是否使用全局对话设置
- 模型设置选项：剩余的选项与全局设置选项含义一致，见下一小节

### 全局设置项的含义

- model / temperature / top_p / max_tokens / presence_penalty / frequency_penalty 均为 ChatGPT 的设置参数，详情请查阅 OpenAI 官方文档，再次不再赘述；
- 注入系统级提示信息、用户输入预处理：详情请看 [https://github.com/Yidadaa/ChatGPT-Next-Web/issues/2144](https://github.com/Yidadaa/ChatGPT-Next-Web/issues/2144)
- 附带历史消息数：用户每次输入消息并发送时，所携带的最近 n 条消息数量；
- 历史消息长度压缩阈值：当已经产生的聊天字数达到该数值以后，则自动触发历史摘要功能；
- 历史摘要：是否启用历史摘要功能。

### 什么是历史摘要？

历史摘要功能，也是历史消息压缩功能，是保证长对话场景下保持历史记忆的关键，合理使用该功能可以在不丢失历史话题信息的情况下，节省所使用的 token。

由于 ChatGPT API 的长度限制，我们以 3.5 模型为例，它只能接受小于 4096 tokens 的对话消息，一旦超出这个数值，就会报错。

同时为了让 ChatGPT 理解我们对话的上下文，往往会携带多条历史消息来提供上下文信息，而当对话进行一段时间之后，很容易就会触发长度限制。

为了解决此问题，我们增加了历史记录压缩功能，假设阈值为 1000 字符，那么每次用户产生的聊天记录超过 1000 字符时，都会将没有被总结过的消息，发送给 ChatGPT，让其产生一个 100 字所有的摘要。

这样，历史信息就从 1000 字压缩到了 100 字，这是一种有损压缩，但已能满足大多数使用场景。

### 什么时候应该关闭历史摘要？

历史摘要可能会影响 ChatGPT 的对话质量，所以如果对话场景是翻译、信息提取等一次性对话场景，请直接关闭历史摘要功能，并将历史消息数设置为 0。

### 当用户发送一条消息时，有哪些信息被发送出去了？

当用户在对话框输入了一条消息后，发送给 ChatGPT 的消息，包含以下几个部分：

1. 系统级提示词：用于尽可能贴近 ChatGPT 官方 WebUI 的使用体验，可在设置中关闭此信息；
2. 历史摘要：作为长期记忆，提供长久但模糊的上下文信息；
3. 预设提示词：当前对话内设置的预设提示词，用于 In-Context Learning 或者注入系统级限制；
4. 最近 n 条对话记录：作为短期记忆，提供短暂但精确的上下文信息；
5. 用户当前输入的消息。
